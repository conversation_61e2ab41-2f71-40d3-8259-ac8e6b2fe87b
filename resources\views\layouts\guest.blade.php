<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}">

    <!-- Favicon -->
    <link rel="icon" href="{{url('theme/web/img/faviconl1.png')}}" type="image/png">

    <!-- Title -->
    <title>{{ config('app.name', 'JobOctopus') }}</title>

    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">

    <!-- Vite Assets (Tailwind CSS + Alpine.js) -->
    @vite(['resources/css/app.css', 'resources/js/app.js'])

    <!-- Custom Auth Styles -->
    <style>

        .floating-shape {
            position: absolute;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.1);
            animation: float 6s ease-in-out infinite;
        }
        .floating-shape:nth-child(1) {
            width: 80px;
            height: 80px;
            top: 10%;
            left: 10%;
            animation-delay: 0s;
        }
        .floating-shape:nth-child(2) {
            width: 120px;
            height: 120px;
            top: 20%;
            right: 10%;
            animation-delay: 2s;
        }
        .floating-shape:nth-child(3) {
            width: 60px;
            height: 60px;
            bottom: 20%;
            left: 20%;
            animation-delay: 4s;
        }
        .floating-shape:nth-child(4) {
            width: 100px;
            height: 100px;
            bottom: 10%;
            right: 20%;
            animation-delay: 1s;
        }
        @keyframes float {
            0%, 100% {
                transform: translateY(0px) rotate(0deg);
            }
            50% {
                transform: translateY(-20px) rotate(180deg);
            }
        }

    </style>
</head>
<body class="font-inter antialiased">
    <!-- Full Screen Auth Container -->
    <div class="min-h-screen bg-gradient-to-br from-blue-500 via-purple-600 to-purple-700 flex items-center justify-center relative overflow-hidden">
        <!-- Floating Background Shapes -->
        <div class="floating-shape"></div>
        <div class="floating-shape"></div>
        <div class="floating-shape"></div>
        <div class="floating-shape"></div>
        
        <!-- Auth Content -->
        <div class="w-full max-w-md px-6 py-4 relative z-10">
            <!-- Logo and Branding -->
            <div class="text-center mb-8">
                <div class="mb-6">
                    <img src="{{url('theme/web/img/logo.png')}}" alt="JobOctopus" class="mx-auto h-16 w-auto drop-shadow-lg">
                </div>
                <h1 class="text-3xl font-bold text-white mb-2">Welcome to JobOctopus</h1>
                <p class="text-white/80 text-sm">Your gateway to amazing career opportunities</p>
            </div>
            
            <!-- Auth Form Card -->
            <div class="backdrop-blur-xl bg-white/95 shadow-2xl rounded-2xl p-8 border border-white/20">
                {{ $slot }}
            </div>
            
            <!-- Footer Links -->
            <div class="text-center mt-6">
                <div class="flex justify-center space-x-6 text-sm">
                    <a href="{{url('/')}}" class="text-white/70 hover:text-white transition-colors">
                        <i class="fa fa-home mr-1"></i>
                        Back to Home
                    </a>
                    <a href="{{url('about')}}" class="text-white/70 hover:text-white transition-colors">
                        About Us
                    </a>
                    <a href="{{url('contact')}}" class="text-white/70 hover:text-white transition-colors">
                        Contact
                    </a>
                </div>
                <div class="mt-4 text-xs text-white/60">
                    &copy; {{ date('Y') }} JobOctopus. All rights reserved.
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script>
        // Add some interactive effects
        document.addEventListener('DOMContentLoaded', function() {
            // Add subtle parallax effect to floating shapes
            document.addEventListener('mousemove', function(e) {
                const shapes = document.querySelectorAll('.floating-shape');
                const x = e.clientX / window.innerWidth;
                const y = e.clientY / window.innerHeight;
                
                shapes.forEach((shape, index) => {
                    const speed = (index + 1) * 0.5;
                    const xPos = (x - 0.5) * speed;
                    const yPos = (y - 0.5) * speed;
                    shape.style.transform = `translate(${xPos}px, ${yPos}px)`;
                });
            });
        });
    </script>
</body>
</html>
