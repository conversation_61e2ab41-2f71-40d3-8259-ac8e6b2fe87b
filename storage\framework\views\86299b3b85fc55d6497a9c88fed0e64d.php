

<?php $__env->startSection('title', $jobListing->title); ?>

<?php $__env->startSection('content'); ?>
<div class="max-w-4xl mx-auto py-6">
    <h1 class="text-3xl font-bold mb-4"><?php echo e($jobListing->title); ?></h1>

    <div class="mb-6">
        <div class="flex items-center space-x-4">
            <?php if($jobListing->company && $jobListing->company->logo): ?>
                <img src="<?php echo e(Storage::url($jobListing->company->logo)); ?>" alt="<?php echo e($jobListing->company->name); ?>" class="h-16 w-16 rounded-full">
            <?php endif; ?>
            <div>
                <h2 class="text-xl font-semibold"><?php echo e($jobListing->company->name ?? 'No Company'); ?></h2>
                <p class="text-gray-600"><?php echo e($jobListing->location); ?></p>
                <p class="text-gray-600">Status: <span class="font-semibold"><?php echo e(ucfirst($jobListing->status)); ?></span></p>
                <p class="text-gray-600">Job Type: <span class="font-semibold"><?php echo e(ucfirst($jobListing->job_type)); ?></span></p>
            </div>
        </div>
    </div>

    <div class="mb-6">
        <h3 class="text-xl font-semibold mb-2">Description</h3>
        <p class="whitespace-pre-line"><?php echo e($jobListing->description); ?></p>
    </div>

    <?php if($jobListing->responsibilities && count($jobListing->responsibilities) > 0): ?>
    <div class="mb-6">
        <h3 class="text-xl font-semibold mb-2">Responsibilities</h3>
        <ul class="list-disc list-inside">
            <?php $__currentLoopData = $jobListing->responsibilities; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $responsibility): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <li><?php echo e($responsibility); ?></li>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </ul>
    </div>
    <?php endif; ?>

    <?php if($jobListing->requirements && count($jobListing->requirements) > 0): ?>
    <div class="mb-6">
        <h3 class="text-xl font-semibold mb-2">Requirements</h3>
        <ul class="list-disc list-inside">
            <?php $__currentLoopData = $jobListing->requirements; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $requirement): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <li><?php echo e($requirement); ?></li>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </ul>
    </div>
    <?php endif; ?>

    <?php if($jobListing->benefits && count($jobListing->benefits) > 0): ?>
    <div class="mb-6">
        <h3 class="text-xl font-semibold mb-2">Benefits</h3>
        <ul class="list-disc list-inside">
            <?php $__currentLoopData = $jobListing->benefits; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $benefit): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <li><?php echo e($benefit); ?></li>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </ul>
    </div>
    <?php endif; ?>

    <div class="mb-6">
        <h3 class="text-xl font-semibold mb-2">Additional Information</h3>
        <p><strong>Salary:</strong> 
            <?php if($jobListing->salary_min || $jobListing->salary_max): ?>
                <?php echo e($jobListing->salary_min ?? 'N/A'); ?> - <?php echo e($jobListing->salary_max ?? 'N/A'); ?> <?php echo e($jobListing->salary_currency ?? ''); ?> / <?php echo e($jobListing->salary_period ?? ''); ?>

            <?php else: ?>
                Not specified
            <?php endif; ?>
        </p>
        <p><strong>Application Deadline:</strong> <?php echo e($jobListing->application_deadline ? $jobListing->application_deadline->format('M d, Y') : 'N/A'); ?></p>
        <p><strong>Experience Level:</strong> <?php echo e($jobListing->experience_level ?? 'N/A'); ?></p>
        <p><strong>Education Level:</strong> <?php echo e($jobListing->education_level ?? 'N/A'); ?></p>
        <p><strong>Skills Required:</strong> <?php echo e($jobListing->skills_required ? implode(', ', $jobListing->skills_required) : 'N/A'); ?></p>
        <p><strong>Application URL:</strong> 
            <?php if($jobListing->application_url): ?>
                <a href="<?php echo e($jobListing->application_url); ?>" target="_blank" class="text-indigo-600 underline"><?php echo e($jobListing->application_url); ?></a>
            <?php else: ?>
                N/A
            <?php endif; ?>
        </p>
        <p><strong>Contact Email:</strong> <?php echo e($jobListing->contact_email ?? 'N/A'); ?></p>
    </div>

    <div class="flex space-x-4">
        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('update', $jobListing)): ?>
            <a href="<?php echo e(route('jobs-listing.edit', $jobListing)); ?>" class="px-4 py-2 bg-yellow-500 text-white rounded hover:bg-yellow-600">Edit</a>
        <?php endif; ?>
        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('delete', $jobListing)): ?>
            <form action="<?php echo e(route('jobs-listing.destroy', $jobListing)); ?>" method="POST" onsubmit="return confirm('Are you sure you want to delete this job listing?');">
                <?php echo csrf_field(); ?>
                <?php echo method_field('DELETE'); ?>
                <button type="submit" class="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700">Delete</button>
            </form>
        <?php endif; ?>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('backend.layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\laragon\www\joboctopus\resources\views/backend/job-listings/show.blade.php ENDPATH**/ ?>