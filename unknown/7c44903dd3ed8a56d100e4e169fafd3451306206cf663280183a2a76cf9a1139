<x-app-backend-layout>
    <div x-data="activationSettings()" class="container mx-auto px-4 py-6">
        <!-- Loading spinner for AJAX requests -->
        <div x-show="isLoading" class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
            <div class="bg-white p-4 rounded-lg flex flex-col items-center">
                <svg class="animate-spin h-10 w-10 text-blue-600 mb-2" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                <span class="text-gray-700">{{ __('Processing') }}...</span>
            </div>
        </div>

        <!-- System Section -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-8">
            <h4 class="text-xl font-medium text-gray-600 mb-6">{{ __('System Settings') }}</h4>

            <div class="flex flex-wrap gap-4">
                
                <!-- HTTPS Activation -->
                <div class="p-4 border border-gray-200 rounded-lg flex-1">
                    <div x-data="{ isEnabled: settings.FORCE_HTTPS }" class="w-full">
                        <label class="inline-flex items-center justify-between w-full cursor-pointer">
                            <span class="text-base font-medium text-gray-700">{{ __('HTTPS Activation') }}</span>
                            
                            <label class="toggle-switch">
                                <input type="checkbox" 
                                       x-model="isEnabled" 
                                       @change="updateSetting('FORCE_HTTPS', isEnabled ? 1 : 0)">
                                <span class="toggle-track">
                                    <span class="toggle-knob"></span>
                                </span>
                            </label>
                        </label>
                        <p class="mt-2 text-sm text-gray-500">{{ __('Enable HTTPS protocol for secure connections') }}</p>
                    </div>
                </div>

                <!-- Maintenance Mode Activation -->
                <div class="p-4 border border-gray-200 rounded-lg flex-1">
                    <div x-data="{ isEnabled: settings.maintenance_mode }" class="w-full">
                        <label class="inline-flex items-center justify-between w-full cursor-pointer">
                            <span class="text-base font-medium text-gray-700">{{ __('Maintenance Mode') }}</span>
                            
                            <label class="toggle-switch">
                                <input type="checkbox" 
                                       id="maintenance_mode"
                                       x-model="isEnabled" 
                                       @change="updateSetting('maintenance_mode', isEnabled ? 1 : 0)">
                                <span class="toggle-track">
                                    <span class="toggle-knob"></span>
                                </span>
                            </label>
                        </label>
                        <p class="mt-2 text-sm text-gray-500">{{ __('Put the site in maintenance mode for visitors') }}</p>
                    </div>
                </div>

                <!-- Disable image encoding -->
                <div class="p-4 border border-gray-200 rounded-lg flex-1">
                    <div x-data="{ isEnabled: settings.disable_image_optimization }" class="w-full">
                        <label class="inline-flex items-center justify-between w-full cursor-pointer">
                            <span class="text-base font-medium text-gray-700">{{ __('Disable image encoding') }}</span>
                            
                            <label class="toggle-switch">
                                <input type="checkbox" 
                                       id="disable_image_optimization"
                                       x-model="isEnabled" 
                                       @change="updateSetting('disable_image_optimization', isEnabled ? 1 : 0)">
                                <span class="toggle-track">
                                    <span class="toggle-knob"></span>
                                </span>
                            </label>
                        </label>
                        <p class="mt-2 text-sm text-gray-500">{{ __('Turn off automatic image optimization') }}</p>
                    </div>
                </div>
                
                
                
            </div>
        </div>

        <!-- Business Related Section -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-8">
            <h4 class="text-xl font-medium text-gray-600 mb-6">{{ __('Business Features') }}</h4>

            <div class="flex flex-wrap gap-4 mb-4">
                
                <!-- Vendor System Activation -->
                <div class="p-4 border border-gray-200 rounded-lg flex-1">
                    <div x-data="{ isEnabled: settings.vendor_system_activation }" class="w-full">
                        <label class="inline-flex items-center justify-between w-full cursor-pointer">
                            <span class="text-base font-medium text-gray-700">{{ __('Vendor System') }}</span>
                            
                            <label class="toggle-switch">
                                <input type="checkbox" 
                                       id="vendor_system_activation"
                                       x-model="isEnabled" 
                                       @change="updateSetting('vendor_system_activation', isEnabled ? 1 : 0)">
                                <span class="toggle-track">
                                    <span class="toggle-knob"></span>
                                </span>
                            </label>
                        </label>
                        <p class="mt-2 text-sm text-gray-500">{{ __('Enable multi-vendor marketplace') }}</p>
                    </div>
                </div>

                <!-- Classified Product -->
                <div class="p-4 border border-gray-200 rounded-lg flex-1">
                    <div x-data="{ isEnabled: settings.classified_product }" class="w-full">
                        <label class="inline-flex items-center justify-between w-full cursor-pointer">
                            <span class="text-base font-medium text-gray-700">{{ __('Classified Product') }}</span>
                            
                            <label class="toggle-switch">
                                <input type="checkbox" 
                                       id="classified_product"
                                       x-model="isEnabled" 
                                       @change="updateSetting('classified_product', isEnabled ? 1 : 0)">
                                <span class="toggle-track">
                                    <span class="toggle-knob"></span>
                                </span>
                            </label>
                        </label>
                        <p class="mt-2 text-sm text-gray-500">{{ __('Allow classified product listings') }}</p>
                    </div>
                </div>

                <!-- Wallet System Activation -->
                <div class="p-4 border border-gray-200 rounded-lg flex-1">
                    <div x-data="{ isEnabled: settings.wallet_system }" class="w-full">
                        <label class="inline-flex items-center justify-between w-full cursor-pointer">
                            <span class="text-base font-medium text-gray-700">{{ __('Wallet System') }}</span>
                            
                            <label class="toggle-switch">
                                <input type="checkbox" 
                                       id="wallet_system"
                                       x-model="isEnabled" 
                                       @change="updateSetting('wallet_system', isEnabled ? 1 : 0)">
                                <span class="toggle-track">
                                    <span class="toggle-knob"></span>
                                </span>
                            </label>
                        </label>
                        <p class="mt-2 text-sm text-gray-500">{{ __('Enable customer wallet system') }}</p>
                    </div>
                </div>
                
            </div>

            <div class="flex flex-wrap gap-4">
                
                <!-- Coupon System Activation -->
                <div class="p-4 border border-gray-200 rounded-lg flex-1">
                    <div x-data="{ isEnabled: settings.coupon_system }" class="w-full">
                        <label class="inline-flex items-center justify-between w-full cursor-pointer">
                            <span class="text-base font-medium text-gray-700">{{ __('Coupon System') }}</span>
                            
                            <label class="toggle-switch">
                                <input type="checkbox" 
                                       id="coupon_system"
                                       x-model="isEnabled" 
                                       @change="updateSetting('coupon_system', isEnabled ? 1 : 0)">
                                <span class="toggle-track">
                                    <span class="toggle-knob"></span>
                                </span>
                            </label>
                        </label>
                        <p class="mt-2 text-sm text-gray-500">{{ __('Allow discount coupons') }}</p>
                    </div>
                </div>

                <!-- Pickup Point Activation -->
                <div class="p-4 border border-gray-200 rounded-lg flex-1">
                    <div x-data="{ isEnabled: settings.pickup_point }" class="w-full">
                        <label class="inline-flex items-center justify-between w-full cursor-pointer">
                            <span class="text-base font-medium text-gray-700">{{ __('Pickup Point') }}</span>
                            
                            <label class="toggle-switch">
                                <input type="checkbox" 
                                       id="pickup_point"
                                       x-model="isEnabled" 
                                       @change="updateSetting('pickup_point', isEnabled ? 1 : 0)">
                                <span class="toggle-track">
                                    <span class="toggle-knob"></span>
                                </span>
                            </label>
                        </label>
                        <p class="mt-2 text-sm text-gray-500">{{ __('Enable in-store pickup') }}</p>
                    </div>
                </div>

                <!-- Conversation Activation -->
                <div class="p-4 border border-gray-200 rounded-lg flex-1">
                    <div x-data="{ isEnabled: settings.conversation_system }" class="w-full">
                        <label class="inline-flex items-center justify-between w-full cursor-pointer">
                            <span class="text-base font-medium text-gray-700">{{ __('Conversation') }}</span>
                            
                            <label class="toggle-switch">
                                <input type="checkbox" 
                                       id="conversation_system"
                                       x-model="isEnabled" 
                                       @change="updateSetting('conversation_system', isEnabled ? 1 : 0)">
                                <span class="toggle-track">
                                    <span class="toggle-knob"></span>
                                </span>
                            </label>
                        </label>
                        <p class="mt-2 text-sm text-gray-500">{{ __('Enable messaging system') }}</p>
                    </div>
                </div>
                
            </div>
        </div>

        <!-- Admin Options Section -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-8">
            <h4 class="text-xl font-medium text-gray-600 mb-6">{{ __('Admin Settings') }}</h4>

            <div class="flex flex-wrap gap-4">
                
                <!-- Seller Product Manage By Admin -->
                <div class="p-4 border border-gray-200 rounded-lg flex-1">
                    <div x-data="{ isEnabled: settings.product_manage_by_admin }" class="w-full">
                        <div class="flex items-center justify-between w-full cursor-pointer mb-2">
                            <span class="text-base font-medium text-gray-700">{{ __('Admin Product Management') }}</span>
                            
                            <label class="toggle-switch">
                                <input type="checkbox" 
                                       id="product_manage_by_admin"
                                       x-model="isEnabled" 
                                       @change="updateSetting('product_manage_by_admin', isEnabled ? 1 : 0)">
                                <span class="toggle-track">
                                    <span class="toggle-knob"></span>
                                </span>
                            </label>
                        </div>
                        <div class="text-sm text-blue-700 bg-blue-50 p-3 rounded-md">
                            {{ __('Cash On Delivery of Seller product will be managed by Admin') }}
                        </div>
                    </div>
                </div>

                <!-- Admin Approval On Seller Product -->
                <div class="p-4 border border-gray-200 rounded-lg flex-1">
                    <div x-data="{ isEnabled: settings.product_approve_by_admin }" class="w-full">
                        <div class="flex items-center justify-between w-full cursor-pointer mb-2">
                            <span class="text-base font-medium text-gray-700">{{ __('Admin Product Approval') }}</span>
                            
                            <label class="toggle-switch">
                                <input type="checkbox" 
                                       id="product_approve_by_admin"
                                       x-model="isEnabled" 
                                       @change="updateSetting('product_approve_by_admin', isEnabled ? 1 : 0)">
                                <span class="toggle-track">
                                    <span class="toggle-knob"></span>
                                </span>
                            </label>
                        </div>
                        <div class="text-sm text-blue-700 bg-blue-50 p-3 rounded-md">
                            {{ __('Admin approval required for seller products') }}
                        </div>
                    </div>
                </div>
                
                
            </div>
        </div>
        
        <!-- Social Media Links Section -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-8">
            <h4 class="text-xl font-medium text-gray-600 mb-6">{{ __('Social Media Links') }}</h4>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- Facebook Link -->
                <div class="p-4 border border-gray-200 rounded-lg">
                    <label for="facebook_url" class="block mb-2 text-base font-medium text-gray-700">
                        {{ __('Facebook Page URL') }}
                    </label>
                    <input 
                        type="text" 
                        id="facebook_url" 
                        x-model="settings.facebook_url" 
                        @change="updateSetting('facebook_url', settings.facebook_url)"
                        placeholder="{{ __('Enter Facebook Page URL') }}"
                        class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    >
                    <p class="mt-2 text-sm text-gray-500">
                        {{ __('Full URL to your Facebook page. Leave blank to hide icon.') }}
                    </p>
                </div>

                <!-- Instagram Link -->
                <div class="p-4 border border-gray-200 rounded-lg">
                    <label for="instagram_url" class="block mb-2 text-base font-medium text-gray-700">
                        {{ __('Instagram Page URL') }}
                    </label>
                    <input 
                        type="text" 
                        id="instagram_url" 
                        x-model="settings.instagram_url" 
                        @change="updateSetting('instagram_url', settings.instagram_url)"
                        placeholder="{{ __('Enter Instagram Page URL') }}"
                        class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    >
                    <p class="mt-2 text-sm text-gray-500">
                        {{ __('Full URL to your Instagram page. Leave blank to hide icon.') }}
                    </p>
                </div>

                <!-- Twitter Link -->
                <div class="p-4 border border-gray-200 rounded-lg">
                    <label for="twitter_url" class="block mb-2 text-base font-medium text-gray-700">
                        {{ __('Twitter/X Page URL') }}
                    </label>
                    <input 
                        type="text" 
                        id="twitter_url" 
                        x-model="settings.twitter_url" 
                        @change="updateSetting('twitter_url', settings.twitter_url)"
                        placeholder="{{ __('Enter Twitter/X Page URL') }}"
                        class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    >
                    <p class="mt-2 text-sm text-gray-500">
                        {{ __('Full URL to your Twitter/X page. Leave blank to hide icon.') }}
                    </p>
                </div>
            </div>
        </div>
        
        <!-- Email Configuration Section -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-8">
            <h4 class="text-xl font-medium text-gray-600 mb-6">{{ __('Email Settings') }}</h4>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- Mail From Address -->
                <div class="p-4 border border-gray-200 rounded-lg">
                    <label for="mail_from_address" class="block mb-2 text-base font-medium text-gray-700">
                        {{ __('Sender Email Address') }}
                    </label>
                    <input 
                        type="email" 
                        id="mail_from_address" 
                        x-model="settings.mail_from_address" 
                        @change="updateSetting('mail_from_address', settings.mail_from_address)"
                        placeholder="{{ __('Enter email address for sending emails') }}"
                        class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    >
                    <p class="mt-2 text-sm text-gray-500">
                        {{ __('This address will appear as the sender in all outgoing emails') }}
                    </p>
                </div>

                <!-- Admin Email -->
                <div class="p-4 border border-gray-200 rounded-lg">
                    <label for="admin_email" class="block mb-2 text-base font-medium text-gray-700">
                        {{ __('Admin Email Address') }}
                    </label>
                    <input 
                        type="email" 
                        id="admin_email" 
                        x-model="settings.admin_email" 
                        @change="updateSetting('admin_email', settings.admin_email)"
                        placeholder="{{ __('Enter admin email address') }}"
                        class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    >
                    <p class="mt-2 text-sm text-gray-500">
                        {{ __('Email address that will receive contact form submissions') }}
                    </p>
                </div>

                <!-- BCC Emails -->
                <div class="p-4 border border-gray-200 rounded-lg">
                    <label for="bcc_emails" class="block mb-2 text-base font-medium text-gray-700">
                        {{ __('BCC Email Addresses') }}
                    </label>
                    <input 
                        type="text" 
                        id="bcc_emails" 
                        x-model="settings.bcc_emails" 
                        @change="updateSetting('bcc_emails', settings.bcc_emails)"
                        placeholder="{{ __('Enter BCC email addresses (comma separated)') }}"
                        class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    >
                    <p class="mt-2 text-sm text-gray-500">
                        {{ __('Additional emails that will receive copies of form submissions (separate with commas)') }}
                    </p>
                </div>
                
                <!-- SMTP Host -->
                <div class="p-4 border border-gray-200 rounded-lg">
                    <label for="smtp_host" class="block mb-2 text-base font-medium text-gray-700">
                        {{ __('SMTP Host') }}
                    </label>
                    <input 
                        type="text" 
                        id="smtp_host" 
                        x-model="settings.smtp_host" 
                        @change="updateSetting('smtp_host', settings.smtp_host)"
                        placeholder="{{ __('Enter SMTP Host') }}"
                        class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    >
                    <p class="mt-2 text-sm text-gray-500">
                        {{ __('SMTP server hostname (e.g., smtp.gmail.com)') }}
                    </p>
                </div>
                
                <!-- SMTP Port -->
                <div class="p-4 border border-gray-200 rounded-lg">
                    <label for="smtp_port" class="block mb-2 text-base font-medium text-gray-700">
                        {{ __('SMTP Port') }}
                    </label>
                    <input 
                        type="text" 
                        id="smtp_port" 
                        x-model="settings.smtp_port" 
                        @change="updateSetting('smtp_port', settings.smtp_port)"
                        placeholder="{{ __('Enter SMTP Port') }}"
                        class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    >
                    <p class="mt-2 text-sm text-gray-500">
                        {{ __('SMTP server port (e.g., 587, 465)') }}
                    </p>
                </div>
                
                <!-- SMTP Encryption -->
                <div class="p-4 border border-gray-200 rounded-lg">
                    <label for="smtp_encryption" class="block mb-2 text-base font-medium text-gray-700">
                        {{ __('SMTP Encryption') }}
                    </label>
                    <select 
                        id="smtp_encryption" 
                        x-model="settings.smtp_encryption" 
                        @change="updateSetting('smtp_encryption', settings.smtp_encryption)"
                        class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    >
                        <option value="tls">TLS</option>
                        <option value="ssl">SSL</option>
                        <option value="">None</option>
                    </select>
                    <p class="mt-2 text-sm text-gray-500">
                        {{ __('Email server encryption method') }}
                    </p>
                </div>

                <!-- SMTP Username -->
                <div class="p-4 border border-gray-200 rounded-lg">
                    <label for="smtp_username" class="block mb-2 text-base font-medium text-gray-700">
                        {{ __('SMTP Username') }}
                    </label>
                    <input 
                        type="text" 
                        id="smtp_username" 
                        x-model="settings.smtp_username" 
                        @change="updateSetting('smtp_username', settings.smtp_username)"
                        placeholder="{{ __('Enter SMTP username') }}"
                        class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    >
                    <p class="mt-2 text-sm text-gray-500">
                        {{ __('Username for SMTP authentication') }}
                    </p>
                </div>

                <!-- SMTP Password -->
                <div class="p-4 border border-gray-200 rounded-lg md:col-span-2">
                    <label for="smtp_password" class="block mb-2 text-base font-medium text-gray-700">
                        {{ __('SMTP Password') }}
                    </label>
                    <div class="relative">
                        <input 
                            type="password" 
                            id="smtp_password" 
                            x-model="settings.smtp_password" 
                            @change="updateSetting('smtp_password', settings.smtp_password)"
                            placeholder="{{ __('Enter SMTP password') }}"
                            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                        >
                        <div class="absolute inset-y-0 right-0 flex items-center pr-3">
                            <button 
                                type="button" 
                                @click="document.getElementById('smtp_password').type = document.getElementById('smtp_password').type === 'password' ? 'text' : 'password'" 
                                class="text-gray-500 hover:text-gray-700 focus:outline-none"
                            >
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                                </svg>
                            </button>
                        </div>
                    </div>
                    <p class="mt-2 text-sm text-gray-500">
                        {{ __('Password for SMTP authentication') }}
                    </p>
                </div>

                <!-- Test Email Button -->
                <div class="p-4 border border-gray-200 rounded-lg md:col-span-2">
                    <div class="flex justify-between items-center">
                        <div>
                            <h5 class="text-base font-medium text-gray-700 mb-1">{{ __('Test Email Configuration') }}</h5>
                            <p class="text-sm text-gray-500">
                                {{ __('Send a test email to verify your SMTP settings') }}
                            </p>
                        </div>
                        <button 
                            type="button" 
                            x-on:click="sendTestEmail"
                            class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
                        >
                            {{ __('Send Test Email') }}
                        </button>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Legal Pages Configuration Section -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-8">
            <h4 class="text-xl font-medium text-gray-600 mb-6">{{ __('Legal Pages') }}</h4>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- Privacy Policy Page -->
                <div class="p-4 border border-gray-200 rounded-lg">
                    <label for="privacy_policy_url" class="block mb-2 text-base font-medium text-gray-700">
                        {{ __('Privacy Policy Page URL') }}
                    </label>
                    <input 
                        type="text" 
                        id="privacy_policy_url" 
                        x-model="settings.privacy_policy_url" 
                        @change="updateSetting('privacy_policy_url', settings.privacy_policy_url)"
                        placeholder="{{ __('Enter Privacy Policy Page URL') }}"
                        class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    >
                    <p class="mt-2 text-sm text-gray-500">
                        {{ __('URL for the Privacy Policy page. Leave blank to hide in footer.') }}
                    </p>
                </div>

                <!-- Terms and Conditions Page -->
                <div class="p-4 border border-gray-200 rounded-lg">
                    <label for="terms_conditions_url" class="block mb-2 text-base font-medium text-gray-700">
                        {{ __('Terms & Conditions Page URL') }}
                    </label>
                    <input 
                        type="text" 
                        id="terms_conditions_url" 
                        x-model="settings.terms_conditions_url" 
                        @change="updateSetting('terms_conditions_url', settings.terms_conditions_url)"
                        placeholder="{{ __('Enter Terms & Conditions Page URL') }}"
                        class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    >
                    <p class="mt-2 text-sm text-gray-500">
                        {{ __('URL for the Terms & Conditions page. Leave blank to hide in footer.') }}
                    </p>
                </div>

                <!-- Return/Refund Policy Page -->
                <div class="p-4 border border-gray-200 rounded-lg">
                    <label for="return_policy_url" class="block mb-2 text-base font-medium text-gray-700">
                        {{ __('Return Policy Page URL') }}
                    </label>
                    <input 
                        type="text" 
                        id="return_policy_url" 
                        x-model="settings.return_policy_url" 
                        @change="updateSetting('return_policy_url', settings.return_policy_url)"
                        placeholder="{{ __('Enter Return Policy Page URL') }}"
                        class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    >
                    <p class="mt-2 text-sm text-gray-500">
                        {{ __('URL for the Return Policy page. Leave blank to hide in footer.') }}
                    </p>
                </div>
            </div>
        </div>
        
    </div>

    <script type="text/javascript">
        function activationSettings() {
            return {
                isLoading: false,
                settings: {
                    FORCE_HTTPS: {{ env('FORCE_HTTPS') == 'On' ? 'true' : 'false' }},
                    maintenance_mode: {{ get_setting('maintenance_mode') == 1 ? 'true' : 'false' }},
                    disable_image_optimization: {{ get_setting('disable_image_optimization') == 1 ? 'true' : 'false' }},
                    vendor_system_activation: {{ get_setting('vendor_system_activation') == 1 ? 'true' : 'false' }},
                    classified_product: {{ get_setting('classified_product') == 1 ? 'true' : 'false' }},
                    wallet_system: {{ get_setting('wallet_system') == 1 ? 'true' : 'false' }},
                    coupon_system: {{ get_setting('coupon_system') == 1 ? 'true' : 'false' }},
                    pickup_point: {{ get_setting('pickup_point') == 1 ? 'true' : 'false' }},
                    conversation_system: {{ get_setting('conversation_system') == 1 ? 'true' : 'false' }},
                    product_manage_by_admin: {{ get_setting('product_manage_by_admin') == 1 ? 'true' : 'false' }},
                    product_approve_by_admin: {{ get_setting('product_approve_by_admin') == 1 ? 'true' : 'false' }},
                    privacy_policy_url: '{{ get_setting('privacy_policy_url') ?? '' }}',
                    terms_conditions_url: '{{ get_setting('terms_conditions_url') ?? '' }}',
                    return_policy_url: '{{ get_setting('return_policy_url') ?? '' }}',
                    facebook_url: '{{ get_setting('facebook_url') ?? '' }}',
                    instagram_url: '{{ get_setting('instagram_url') ?? '' }}',
                    twitter_url: '{{ get_setting('twitter_url') ?? '' }}',
                    mail_from_address: '{{ get_setting('mail_from_address') ?? '' }}',
                    smtp_username: '{{ get_setting('smtp_username') ?? '' }}',
                    smtp_password: '{{ get_setting('smtp_password') ?? '' }}',
                    admin_email: '{{ get_setting('admin_email') ?? '' }}',
                    smtp_host: '{{ get_setting('smtp_host') ?? '' }}',
                    smtp_port: '{{ get_setting('smtp_port') ?? '' }}',
                    smtp_encryption: '{{ get_setting('smtp_encryption') ?? 'tls' }}'
                },
                
                /**
                 * Update setting via AJAX
                 * @param {string} type - The setting key to update
                 * @param {*} value - The value to set for the setting
                 */
                updateSetting(type, value) {
                    // Check for demo mode
                    if ('{{ env('DEMO_MODE') }}' == 'On') {
                        Toastify({
                            text: "{{ __('Data can not change in demo mode.') }}",
                            duration: 3000,
                            close: true,
                            backgroundColor: "#4b5563",
                        }).showToast();
        
                        // Revert the toggle state or input value
                        if (typeof value === 'boolean') {
                            this.settings[type] = !value;
                        } else {
                            this.settings[type] = this.settings[type];
                        }
                        return;
                    }
        
                    this.isLoading = true;
        
                    // Create form data
                    const formData = new FormData();
                    formData.append('_token', '{{ csrf_token() }}');
                    formData.append('type', type);
                    
                    // Handle different types of values (boolean for toggles, string for URLs)
                    if (typeof value === 'boolean') {
                        formData.append('value', value ? 1 : 0);
                    } else {
                        formData.append('value', value);
                    }
        
                    // Send AJAX request
                    fetch('{{ route('business_settings.update.activation') }}', {
                        method: 'POST',
                        body: formData
                    })
                    .then(response => response.json())
                    .then(data => {
                        this.isLoading = false;
                        if (data == 1) {
                            Toastify({
                                text: "{{ __('Settings updated successfully') }}",
                                duration: 3000,
                                close: true,
                                backgroundColor: "#10b981",
                            }).showToast();
                        } else {
                            Toastify({
                                text: "{{ __('Something went wrong') }}",
                                duration: 3000,
                                close: true,
                                backgroundColor: "#ef4444",
                            }).showToast();
                            
                            // Revert the setting on error
                            if (typeof value === 'boolean') {
                                this.settings[type] = !value;
                            } else {
                                this.settings[type] = this.settings[type];
                            }
                        }
                    })
                    .catch(error => {
                        this.isLoading = false;
                        Toastify({
                            text: "{{ __('Error') }}: " + error.message,
                            duration: 3000,
                            close: true,
                            backgroundColor: "#ef4444",
                        }).showToast();
                        
                        // Revert the setting on error
                        if (typeof value === 'boolean') {
                            this.settings[type] = !value;
                        } else {
                            this.settings[type] = this.settings[type];
                        }
                    });
                },
        
                /**
                 * Send test email to verify email configuration
                 */
                sendTestEmail() {
                    // Check for demo mode
                    if ('{{ env('DEMO_MODE') }}' == 'On') {
                        Toastify({
                            text: "{{ __('Data can not change in demo mode.') }}",
                            duration: 3000,
                            close: true,
                            backgroundColor: "#4b5563",
                        }).showToast();
                        return;
                    }
        
                    // Set loading state
                    this.isLoading = true;
        
                    // Create form data
                    const formData = new FormData();
                    formData.append('_token', '{{ csrf_token() }}');
        
                    // Send AJAX request
                    fetch('{{ route('business_settings.test_email') }}', {
                        method: 'POST',
                        body: formData
                    })
                    .then(response => response.json())
                    .then(data => {
                        this.isLoading = false;
                        
                        // Show toast based on response
                        if (data.success) {
                            Toastify({
                                text: data.message,
                                duration: 3000,
                                close: true,
                                backgroundColor: "#10b981",
                            }).showToast();
                        } else {
                            Toastify({
                                text: data.message,
                                duration: 3000,
                                close: true,
                                backgroundColor: "#ef4444",
                            }).showToast();
                        }
                    })
                    .catch(error => {
                        this.isLoading = false;
                        Toastify({
                            text: "{{ __('Error') }}: " + error.message,
                            duration: 3000,
                            close: true,
                            backgroundColor: "#ef4444",
                        }).showToast();
                    });
                }
            }
        }
    </script>

@if(session('success'))
<script>
    document.addEventListener('DOMContentLoaded', function() {
        Toastify({
            text: "{{ session('success') }}",
            duration: 3000,
            close: true,
            backgroundColor: "#10b981",
        }).showToast();
    });
</script>
@endif

</x-app-backend-layout>
