@tailwind base;
@tailwind components;
@tailwind utilities;

/* Reset and ensure Tail<PERSON> takes precedence */
* {
    box-sizing: border-box;
}

/* Override any conflicting styles */
.font-inter {
    font-family: 'Inter', sans-serif !important;
}

/* Ensure proper spacing and layout */
body {
    margin: 0;
    padding: 0;
    line-height: 1.6;
}

/* Make sure Tailwind utilities work properly */
@layer utilities {
    .bg-gradient-to-r {
        background-image: linear-gradient(to right, var(--tw-gradient-stops)) !important;
    }

    .from-blue-600 {
        --tw-gradient-from: #2563eb !important;
        --tw-gradient-to: rgb(37 99 235 / 0) !important;
        --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to) !important;
    }

    .to-blue-700 {
        --tw-gradient-to: #1d4ed8 !important;
    }



    /* Line clamp utility */
    .line-clamp-2 {
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
    }

    .line-clamp-3 {
        display: -webkit-box;
        -webkit-line-clamp: 3;
        -webkit-box-orient: vertical;
        overflow: hidden;
    }
}

/* Custom components for search results */
@layer components {

    /* Enhanced form inputs */
    .form-input {
        @apply w-full rounded-lg border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 transition-all duration-200;
    }

    .form-input:focus {
        box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    }

    /* Button animations */
    .btn-primary {
        @apply bg-gradient-to-br from-red-600 to-red-700 hover:from-red-700 hover:to-red-800 text-white font-semibold py-3 px-6 rounded-lg transition-all duration-300 hover:-translate-y-0.5;
    }

    .btn-primary:hover {
        box-shadow: 0 10px 20px rgba(245, 87, 108, 0.3);
    }

    .btn-secondary {
        @apply border border-gray-300 text-gray-700 font-semibold py-3 px-6 rounded-lg transition-all duration-300;
    }

    .btn-secondary:hover {
        @apply bg-gray-50 border-gray-400;
        transform: translateY(-1px);
    }

    /* Advanced filter animations */
    .filter-slide-down {
        animation: slideDown 0.3s ease-out;
    }

    @keyframes slideDown {
        from {
            opacity: 0;
            transform: translateY(-10px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    /* Loading states */
    .loading-shimmer {
        background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
        background-size: 200% 100%;
        animation: shimmer 1.5s infinite;
    }

    @keyframes shimmer {
        0% {
            background-position: -200% 0;
        }
        100% {
            background-position: 200% 0;
        }
    }

    /* Custom scrollbar */
    .custom-scrollbar::-webkit-scrollbar {
        width: 6px;
    }

    .custom-scrollbar::-webkit-scrollbar-track {
        background: #f1f1f1;
        border-radius: 3px;
    }

    .custom-scrollbar::-webkit-scrollbar-thumb {
        background: #c1c1c1;
        border-radius: 3px;
    }

    .custom-scrollbar::-webkit-scrollbar-thumb:hover {
        background: #a8a8a8;
    }
}
