@extends('frontend.layouts.app')

@section('title', 'Jobs at ' . $company->name . ' - JobOctopus')

@section('content')
<div class="min-h-screen bg-gray-50">
    <!-- Company Header -->
    <div class="bg-white shadow-sm">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
            <div class="flex items-center space-x-4">
                <!-- Back Button -->
                <a href="{{route('frontend.company', $company->slug)}}" 
                   class="text-gray-600 hover:text-gray-900 transition-colors">
                    <i class="fa fa-arrow-left text-lg"></i>
                </a>
                
                <!-- Company Logo -->
                <div class="flex-shrink-0">
                    @if($company->logo)
                        <img src="{{url('storage/' . $company->logo)}}" 
                             alt="{{$company->name}}" 
                             class="w-16 h-16 object-contain rounded-lg border border-gray-200 bg-white">
                    @else
                        <div class="w-16 h-16 bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg flex items-center justify-center">
                            <i class="fa fa-building text-white text-lg"></i>
                        </div>
                    @endif
                </div>
                
                <!-- Company Info -->
                <div class="flex-1">
                    <h1 class="text-2xl font-bold text-gray-900">Jobs at {{$company->name}}</h1>
                    @if($company->location)
                        <p class="text-gray-600">{{$company->location}}</p>
                    @endif
                </div>
                
                <!-- View Company Profile -->
                <a href="{{route('frontend.company', $company->slug)}}" 
                   class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors font-medium">
                    View Company Profile
                </a>
            </div>
        </div>
    </div>

    <!-- Jobs Content -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        @if($jobs->count() > 0)
            <!-- Jobs Count -->
            <div class="mb-6">
                <p class="text-gray-600">
                    Showing {{$jobs->count()}} of {{$jobs->total()}} job{{$jobs->total() != 1 ? 's' : ''}}
                </p>
            </div>

            <!-- Jobs List -->
            <div class="space-y-6">
                @foreach($jobs as $job)
                    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow">
                        <div class="flex items-start justify-between">
                            <div class="flex-1">
                                <!-- Job Title -->
                                <h2 class="text-xl font-semibold text-gray-900 mb-3">{{$job->title}}</h2>
                                
                                <!-- Job Details -->
                                <div class="flex flex-wrap items-center gap-4 text-sm text-gray-600 mb-4">
                                    @if($job->location)
                                        <div class="flex items-center">
                                            <i class="fa fa-map-marker-alt mr-2 text-gray-400"></i>
                                            <span>{{$job->location}}</span>
                                        </div>
                                    @endif
                                    
                                    @if($job->job_type)
                                        <div class="flex items-center">
                                            <i class="fa fa-briefcase mr-2 text-gray-400"></i>
                                            <span>{{ucfirst(str_replace('_', ' ', $job->job_type))}}</span>
                                        </div>
                                    @endif
                                    
                                    @if($job->experience_level)
                                        <div class="flex items-center">
                                            <i class="fa fa-user-graduate mr-2 text-gray-400"></i>
                                            <span>{{$job->experience_level}}</span>
                                        </div>
                                    @endif
                                    
                                    @if($job->salary_min && $job->salary_max)
                                        <div class="flex items-center">
                                            <i class="fa fa-dollar-sign mr-2 text-gray-400"></i>
                                            <span>${{number_format($job->salary_min)}} - ${{number_format($job->salary_max)}}</span>
                                        </div>
                                    @endif
                                    
                                    @if($job->remote_option)
                                        <div class="flex items-center">
                                            <i class="fa fa-home mr-2 text-gray-400"></i>
                                            <span>Remote Available</span>
                                        </div>
                                    @endif
                                </div>
                                
                                <!-- Job Description -->
                                @if($job->description)
                                    <p class="text-gray-700 mb-4 line-clamp-3">
                                        {{Str::limit(strip_tags($job->description), 200)}}
                                    </p>
                                @endif
                                
                                <!-- Categories -->
                                @if($job->categories->count() > 0)
                                    <div class="flex flex-wrap gap-2 mb-4">
                                        @foreach($job->categories->take(4) as $category)
                                            <span class="bg-blue-100 text-blue-800 text-xs px-3 py-1 rounded-full">
                                                {{$category->name}}
                                            </span>
                                        @endforeach
                                        @if($job->categories->count() > 4)
                                            <span class="bg-gray-100 text-gray-600 text-xs px-3 py-1 rounded-full">
                                                +{{$job->categories->count() - 4}} more
                                            </span>
                                        @endif
                                    </div>
                                @endif
                                
                                <!-- Skills -->
                                @if($job->skills_required && is_array($job->skills_required) && count($job->skills_required) > 0)
                                    <div class="flex flex-wrap gap-2 mb-4">
                                        @foreach(array_slice($job->skills_required, 0, 5) as $skill)
                                            <span class="bg-gray-100 text-gray-700 text-xs px-2 py-1 rounded">
                                                {{$skill}}
                                            </span>
                                        @endforeach
                                        @if(count($job->skills_required) > 5)
                                            <span class="bg-gray-100 text-gray-600 text-xs px-2 py-1 rounded">
                                                +{{count($job->skills_required) - 5}} more
                                            </span>
                                        @endif
                                    </div>
                                @endif
                                
                                <!-- Posted Date -->
                                <div class="flex items-center text-sm text-gray-500">
                                    <i class="fa fa-clock mr-2"></i>
                                    <span>Posted {{$job->created_at->diffForHumans()}}</span>
                                    @if($job->application_deadline)
                                        <span class="mx-2">•</span>
                                        <span>Apply by {{$job->application_deadline->format('M d, Y')}}</span>
                                    @endif
                                </div>
                            </div>
                            
                            <!-- Action Buttons -->
                            <div class="flex flex-col items-end space-y-3 ml-6">
                                @if($job->is_featured)
                                    <span class="bg-yellow-100 text-yellow-800 text-xs px-2 py-1 rounded-full font-medium">
                                        Featured
                                    </span>
                                @endif
                                
                                <div class="flex flex-col space-y-2">
                                    <a href="#" 
                                       class="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors text-center font-medium">
                                        Apply Now
                                    </a>
                                    <button class="border border-gray-300 text-gray-700 px-6 py-2 rounded-lg hover:bg-gray-50 transition-colors font-medium">
                                        Save Job
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                @endforeach
            </div>

            <!-- Pagination -->
            <div class="mt-8">
                {{ $jobs->links() }}
            </div>
        @else
            <!-- Empty State -->
            <div class="text-center py-16">
                <div class="max-w-md mx-auto">
                    <div class="w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-6">
                        <i class="fa fa-briefcase text-gray-400 text-3xl"></i>
                    </div>
                    <h3 class="text-xl font-semibold text-gray-900 mb-2">No Jobs Available</h3>
                    <p class="text-gray-600 mb-6">
                        {{$company->name}} doesn't have any open positions at the moment. Check back later or follow the company to get notified when new jobs are posted.
                    </p>
                    <div class="flex flex-col sm:flex-row gap-3 justify-center">
                        <a href="{{route('frontend.company', $company->slug)}}" 
                           class="inline-flex items-center px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                            <i class="fa fa-building mr-2"></i>
                            View Company Profile
                        </a>
                        <a href="{{route('frontend.companies')}}" 
                           class="inline-flex items-center px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors">
                            <i class="fa fa-search mr-2"></i>
                            Browse Other Companies
                        </a>
                    </div>
                </div>
            </div>
        @endif
    </div>
</div>


@endsection
