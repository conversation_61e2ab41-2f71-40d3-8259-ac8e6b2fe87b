@extends('frontend.layouts.app')

@section('title', $metaTitle ?? $page->title)
@section('meta_description', $metaDescription ?? '')
@section('meta_keywords', $metaKeywords ?? '')

@section('content')
<div class="min-h-screen bg-gray-50">
    <!-- Hero Section -->
    <section class="gradient-bg relative overflow-hidden py-20">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
            <div class="text-center">
                <h1 class="text-4xl md:text-5xl font-bold text-white mb-6">
                    {{ $page->title }}
                </h1>
                @if($page->meta_description)
                    <p class="text-xl text-white opacity-90 max-w-3xl mx-auto">
                        {{ $page->meta_description }}
                    </p>
                @endif
            </div>
        </div>
    </section>

    <!-- Content Section -->
    <section class="py-16">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            @if($page->content)
                <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-8 mb-12">
                    <div class="prose prose-lg max-w-none">
                        {!! $page->content !!}
                    </div>
                </div>
            @endif

            @if($page->data && isset($page->data['sections']))
                @foreach($page->data['sections'] as $section)
                    <div class="mb-12">
                        @if(isset($section['title']))
                            <div class="text-center mb-8">
                                <h2 class="text-3xl font-bold text-gray-900 mb-4">{{ $section['title'] }}</h2>
                                <div class="w-24 h-1 bg-gradient-to-r from-blue-500 to-red-500 mx-auto rounded-full"></div>
                            </div>
                        @endif
                        
                        @if(isset($section['content']))
                            <div class="prose prose-lg max-w-none mb-8">
                                {!! $section['content'] !!}
                            </div>
                        @endif
                        
                        @if(isset($section['items']) && is_array($section['items']))
                            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                                @foreach($section['items'] as $item)
                                    <div class="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden hover:shadow-lg transition-all duration-300 group">
                                        <div class="p-6">
                                            <div class="flex items-center mb-4">
                                                <div class="w-12 h-12 icon-gradient rounded-lg flex items-center justify-center mr-4">
                                                    <i class="fa fa-book text-white text-xl"></i>
                                                </div>
                                                @if(isset($item['title']))
                                                    <h3 class="text-xl font-semibold text-gray-900 group-hover:text-blue-600 transition-colors">{{ $item['title'] }}</h3>
                                                @endif
                                            </div>
                                            @if(isset($item['description']))
                                                <p class="text-gray-600 mb-4 leading-relaxed">{{ $item['description'] }}</p>
                                            @endif
                                            
                                            <div class="flex items-center justify-between pt-4 border-t border-gray-100">
                                                <div class="flex items-center text-sm text-gray-500">
                                                    <i class="fa fa-clock-o mr-2"></i>
                                                    <span>6-8 weeks</span>
                                                </div>
                                                @if(isset($item['link']))
                                                    <a href="{{ $item['link'] }}" class="bg-gradient-to-r from-red-600 to-red-700 hover:from-red-700 hover:to-red-800 text-white px-4 py-2 rounded-lg text-sm font-semibold hover:shadow-lg transition-all duration-300">
                                                        Enroll Now
                                                    </a>
                                                @endif
                                            </div>
                                        </div>
                                    </div>
                                @endforeach
                            </div>
                        @endif
                    </div>
                @endforeach
            @endif

            <!-- Course Categories -->
            <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-8">
                <div class="text-center mb-8">
                    <h2 class="text-3xl font-bold text-gray-900 mb-4">Course Categories</h2>
                    <div class="w-24 h-1 bg-gradient-to-r from-blue-500 to-red-500 mx-auto rounded-full"></div>
                </div>
                
                <div class="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-6">
                    <div class="text-center p-4 rounded-lg hover:bg-gray-50 transition-colors">
                        <div class="w-16 h-16 icon-gradient rounded-full flex items-center justify-center mx-auto mb-3">
                            <i class="fa fa-code text-white text-xl"></i>
                        </div>
                        <h3 class="text-sm font-semibold text-gray-900">Programming</h3>
                    </div>
                    
                    <div class="text-center p-4 rounded-lg hover:bg-gray-50 transition-colors">
                        <div class="w-16 h-16 red-gradient rounded-full flex items-center justify-center mx-auto mb-3">
                            <i class="fa fa-paint-brush text-white text-xl"></i>
                        </div>
                        <h3 class="text-sm font-semibold text-gray-900">Design</h3>
                    </div>
                    
                    <div class="text-center p-4 rounded-lg hover:bg-gray-50 transition-colors">
                        <div class="w-16 h-16 icon-gradient rounded-full flex items-center justify-center mx-auto mb-3">
                            <i class="fa fa-chart-bar text-white text-xl"></i>
                        </div>
                        <h3 class="text-sm font-semibold text-gray-900">Analytics</h3>
                    </div>
                    
                    <div class="text-center p-4 rounded-lg hover:bg-gray-50 transition-colors">
                        <div class="w-16 h-16 red-gradient rounded-full flex items-center justify-center mx-auto mb-3">
                            <i class="fa fa-bullhorn text-white text-xl"></i>
                        </div>
                        <h3 class="text-sm font-semibold text-gray-900">Marketing</h3>
                    </div>
                    
                    <div class="text-center p-4 rounded-lg hover:bg-gray-50 transition-colors">
                        <div class="w-16 h-16 icon-gradient rounded-full flex items-center justify-center mx-auto mb-3">
                            <i class="fa fa-cogs text-white text-xl"></i>
                        </div>
                        <h3 class="text-sm font-semibold text-gray-900">Engineering</h3>
                    </div>
                    
                    <div class="text-center p-4 rounded-lg hover:bg-gray-50 transition-colors">
                        <div class="w-16 h-16 red-gradient rounded-full flex items-center justify-center mx-auto mb-3">
                            <i class="fa fa-briefcase text-white text-xl"></i>
                        </div>
                        <h3 class="text-sm font-semibold text-gray-900">Business</h3>
                    </div>
                </div>
            </div>
        </div>
    </section>
</div>
@endsection
