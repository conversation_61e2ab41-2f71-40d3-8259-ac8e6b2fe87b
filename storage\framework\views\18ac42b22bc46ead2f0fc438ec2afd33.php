<!DOCTYPE html>
<html lang="<?php echo e(str_replace('_', '-', app()->getLocale())); ?>">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">

    <!-- Favicon -->
    <link rel="icon" href="<?php echo e(url('theme/web/img/faviconl1.png')); ?>" type="image/png">

    <!-- Meta Tags -->
    <meta name="author" content="JobOctopus">
    <meta name="robots" content="<?php echo $__env->yieldContent('robots', 'index, follow'); ?>">

    <?php if(isset($seoData)): ?>
        <?php echo seo_meta_tags($seoData); ?>

    <?php else: ?>
        <title><?php echo $__env->yieldContent('title', 'Jobs-Recruitment-Employment-Career-Courses-Professionals | JobOctopus'); ?></title>
        <meta name="description" content="<?php echo $__env->yieldContent('meta_description', 'Find your dream job with JobOctopus - Jobs, Recruitment, Employment, Career opportunities, Courses and Professional development'); ?>">
        <meta name="keywords" content="<?php echo $__env->yieldContent('meta_keywords', 'jobs, recruitment, employment, career, courses, professionals, job search'); ?>">
        <link rel="canonical" href="<?php echo e(url()->current()); ?>">

        <!-- Open Graph -->
        <meta property="og:title" content="<?php echo $__env->yieldContent('title', 'JobOctopus - Find Your Dream Job'); ?>">
        <meta property="og:description" content="<?php echo $__env->yieldContent('meta_description', 'Find your dream job with JobOctopus - Jobs, Recruitment, Employment, Career opportunities, Courses and Professional development'); ?>">
        <meta property="og:url" content="<?php echo e(url()->current()); ?>">
        <meta property="og:site_name" content="JobOctopus">
        <meta property="og:type" content="website">
        <meta property="og:image" content="<?php echo e(asset('theme/web/img/logo.png')); ?>">

        <!-- Twitter Card -->
        <meta name="twitter:card" content="summary_large_image">
        <meta name="twitter:title" content="<?php echo $__env->yieldContent('title', 'JobOctopus - Find Your Dream Job'); ?>">
        <meta name="twitter:description" content="<?php echo $__env->yieldContent('meta_description', 'Find your dream job with JobOctopus'); ?>">
        <meta name="twitter:image" content="<?php echo e(asset('theme/web/img/logo.png')); ?>">
    <?php endif; ?>

    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="<?php echo e(url('theme/web/css/font-awesome.min.css')); ?>">

    <!-- Vite Assets (Tailwind CSS + Alpine.js) -->
    <?php echo app('Illuminate\Foundation\Vite')(['resources/css/app.css', 'resources/js/app.js']); ?>

    <style>
        <?php echo $__env->yieldContent('styles'); ?>
    </style>
    <?php echo $__env->yieldPushContent('styles'); ?>

    <!-- Structured Data -->
    <?php if(isset($structuredData)): ?>
        <?php echo structured_data($structuredData); ?>

    <?php endif; ?>

    <?php echo $__env->yieldPushContent('structured-data'); ?>
</head>
<body class="font-inter antialiased bg-gray-50" x-data="{ mobileMenuOpen: false }"
      x-init="
        // Add smooth scrolling
        document.documentElement.style.scrollBehavior = 'smooth';

        // Add intersection observer for animations
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('animate-fade-in-up');
                }
            });
        }, observerOptions);

        // Observe elements with animation classes
        document.querySelectorAll('[data-animate]').forEach(el => {
            observer.observe(el);
        });
      ">

    <!-- Navigation -->
    <nav class="bg-white shadow-sm border-b border-gray-200">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-16">
                <!-- Logo -->
                <div class="flex-shrink-0">
                    <a href="<?php echo e(url('/')); ?>" class="flex items-center">
                        <img src="<?php echo e(url('theme/web/img/logo.png')); ?>" alt="JobOctopus" class="h-8 w-auto">
                    </a>
                </div>

                <!-- Desktop Navigation -->
                <div class="hidden md:flex items-center space-x-8">
                    <a href="<?php echo e(route('home')); ?>" class="text-gray-700 hover:text-blue-600 px-3 py-2 text-sm font-medium">Home</a>
                    <a href="<?php echo e(route('jobs.all')); ?>" class="text-gray-700 hover:text-blue-600 px-3 py-2 text-sm font-medium">Jobs</a>
                    <a href="<?php echo e(route('frontend.companies')); ?>" class="text-gray-700 hover:text-blue-600 px-3 py-2 text-sm font-medium">Companies</a>
                    <a href="<?php echo e(route('page.show', 'about')); ?>" class="text-gray-700 hover:text-blue-600 px-3 py-2 text-sm font-medium">About</a>
                    <a href="<?php echo e(route('page.show', 'contact')); ?>" class="text-gray-700 hover:text-blue-600 px-3 py-2 text-sm font-medium">Contact</a>
                </div>

                <!-- Auth Links -->
                <div class="hidden md:flex items-center space-x-4">
                    <?php if(auth()->guard()->check()): ?>
                        <a href="<?php echo e(route('dashboard')); ?>" class="text-gray-700 hover:text-blue-600 px-3 py-2 text-sm font-medium">Dashboard</a>
                        <div class="relative" x-data="{ open: false }">
                            <button @click="open = !open" class="flex items-center text-gray-700 hover:text-blue-600 px-3 py-2 text-sm font-medium">
                                <?php echo e(auth()->user()->name); ?>

                                <svg class="ml-1 h-4 w-4" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                                </svg>
                            </button>
                            <div x-show="open" @click.away="open = false" x-transition class="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-50">
                                <a href="<?php echo e(route('profile.edit')); ?>" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">Profile</a>
                                <form method="POST" action="<?php echo e(route('logout')); ?>">
                                    <?php echo csrf_field(); ?>
                                    <button type="submit" class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">Logout</button>
                                </form>
                            </div>
                        </div>
                    <?php else: ?>
                        <a href="<?php echo e(route('login')); ?>" class="text-gray-700 hover:text-blue-600 px-3 py-2 text-sm font-medium">Login</a>
                        <a href="<?php echo e(route('register')); ?>" class="bg-blue-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-blue-700 transition-colors">Register</a>
                    <?php endif; ?>
                </div>

                <!-- Mobile menu button -->
                <div class="md:hidden">
                    <button @click="mobileMenuOpen = !mobileMenuOpen" class="text-gray-700 hover:text-blue-600">
                        <i class="fa fa-bars text-xl"></i>
                    </button>
                </div>
            </div>
        </div>

        <!-- Mobile Navigation -->
        <div x-show="mobileMenuOpen" x-transition class="md:hidden bg-white border-t border-gray-200">
            <div class="px-2 pt-2 pb-3 space-y-1">
                <a href="<?php echo e(route('home')); ?>" class="block px-3 py-2 text-gray-700 hover:text-blue-600">Home</a>
                <a href="<?php echo e(route('jobs.all')); ?>" class="block px-3 py-2 text-gray-700 hover:text-blue-600">Jobs</a>
                <a href="<?php echo e(route('frontend.companies')); ?>" class="block px-3 py-2 text-gray-700 hover:text-blue-600">Companies</a>
                <a href="<?php echo e(route('page.show', 'about')); ?>" class="block px-3 py-2 text-gray-700 hover:text-blue-600">About</a>
                <a href="<?php echo e(route('page.show', 'contact')); ?>" class="block px-3 py-2 text-gray-700 hover:text-blue-600">Contact</a>

                <?php if(auth()->guard()->check()): ?>
                    <a href="<?php echo e(route('dashboard')); ?>" class="block px-3 py-2 text-gray-700 hover:text-blue-600">Dashboard</a>
                    <a href="<?php echo e(route('profile.edit')); ?>" class="block px-3 py-2 text-gray-700 hover:text-blue-600">Profile</a>
                    <form method="POST" action="<?php echo e(route('logout')); ?>" class="block">
                        <?php echo csrf_field(); ?>
                        <button type="submit" class="w-full text-left px-3 py-2 text-gray-700 hover:text-blue-600">Logout</button>
                    </form>
                <?php else: ?>
                    <a href="<?php echo e(route('login')); ?>" class="block px-3 py-2 text-gray-700 hover:text-blue-600">Login</a>
                    <a href="<?php echo e(route('register')); ?>" class="block px-3 py-2 text-blue-600 font-medium">Register</a>
                <?php endif; ?>
            </div>
        </div>
    </nav>

    <!-- Breadcrumbs -->
    <?php if(isset($breadcrumbs) && !empty($breadcrumbs)): ?>
        <div class="bg-gray-50 border-b border-gray-200">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
                <?php echo breadcrumb_html($breadcrumbs); ?>

            </div>
        </div>
    <?php endif; ?>

    <!-- Main Content -->
    <?php echo $__env->yieldContent('content'); ?>

    <!-- Footer -->
    <footer class="bg-gray-900 text-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
                <div class="md:col-span-1">
                    <img src="<?php echo e(url('theme/web/img/logo.png')); ?>" alt="JobOctopus" class="h-8 w-auto mb-4 filter brightness-0 invert">
                    <p class="text-gray-400 text-sm">Find your dream job with JobOctopus - the leading job portal for professionals.</p>
                </div>
                <div>
                    <h3 class="text-white font-semibold mb-4">For Candidates</h3>
                    <ul class="space-y-2 text-sm text-gray-400">
                        <li><a href="<?php echo e(route('jobs.all')); ?>" class="hover:text-white">Browse Jobs</a></li>
                        <li><a href="<?php echo e(route('register')); ?>" class="hover:text-white">Create Account</a></li>
                        <li><a href="<?php echo e(route('login')); ?>" class="hover:text-white">Login</a></li>
                        <li><a href="<?php echo e(route('page.show', 'faq')); ?>" class="hover:text-white">FAQ</a></li>
                    </ul>
                </div>
                <div>
                    <h3 class="text-white font-semibold mb-4">For Employers</h3>
                    <ul class="space-y-2 text-sm text-gray-400">
                        <li><a href="<?php echo e(route('login')); ?>" class="hover:text-white">Employer Login</a></li>
                        <li><a href="<?php echo e(route('register')); ?>" class="hover:text-white">Post Jobs</a></li>
                        <li><a href="<?php echo e(route('page.show', 'membership')); ?>" class="hover:text-white">Pricing</a></li>
                        <li><a href="<?php echo e(route('page.show', 'advertise')); ?>" class="hover:text-white">Advertise</a></li>
                    </ul>
                </div>
                <div>
                    <h3 class="text-white font-semibold mb-4">Support</h3>
                    <ul class="space-y-2 text-sm text-gray-400">
                        <li><a href="<?php echo e(route('page.show', 'contact')); ?>" class="hover:text-white">Contact Us</a></li>
                        <li><a href="<?php echo e(route('page.show', 'about')); ?>" class="hover:text-white">About Us</a></li>
                        <li><a href="<?php echo e(route('page.show', 'privacy-policy')); ?>" class="hover:text-white">Privacy Policy</a></li>
                        <li><a href="<?php echo e(route('page.show', 'terms-conditions')); ?>" class="hover:text-white">Terms & Conditions</a></li>
                    </ul>
                </div>
            </div>

            <!-- Social Links & Newsletter -->
            <div class="border-t border-gray-800 mt-8 pt-8">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                    <div>
                        <h4 class="text-white font-semibold mb-4">Follow Us</h4>
                        <div class="flex space-x-4">
                            <a href="#" class="text-gray-400 hover:text-white transition-colors">
                                <i class="fa fa-facebook text-xl"></i>
                            </a>
                            <a href="#" class="text-gray-400 hover:text-white transition-colors">
                                <i class="fa fa-twitter text-xl"></i>
                            </a>
                            <a href="#" class="text-gray-400 hover:text-white transition-colors">
                                <i class="fa fa-linkedin text-xl"></i>
                            </a>
                            <a href="#" class="text-gray-400 hover:text-white transition-colors">
                                <i class="fa fa-instagram text-xl"></i>
                            </a>
                            <a href="#" class="text-gray-400 hover:text-white transition-colors">
                                <i class="fa fa-youtube text-xl"></i>
                            </a>
                        </div>
                    </div>
                    <div>
                        <h4 class="text-white font-semibold mb-4">Newsletter</h4>
                        <form class="flex">
                            <input
                                type="email"
                                placeholder="Enter your email"
                                class="flex-1 px-4 py-2 rounded-l-md border-0 text-gray-900 focus:ring-2 focus:ring-blue-500 focus:outline-none"
                            >
                            <button
                                type="submit"
                                class="bg-gradient-to-br from-red-600 to-red-700 text-white px-6 py-2 rounded-r-md hover:shadow-lg hover:from-red-700 hover:to-red-800 transition-all duration-300 transform hover:scale-105 hover:-translate-y-0.5"
                            >
                                Subscribe
                            </button>
                        </form>
                    </div>
                </div>
            </div>

            <div class="border-t border-gray-800 mt-8 pt-8 text-center">
                <p class="text-gray-400 text-sm">
                    Copyright &copy; <span id="current-year"></span> JobOctopus. All rights reserved.
                </p>
            </div>
        </div>
    </footer>

    <!-- Scripts -->
    <script>
        // Set current year
        document.getElementById('current-year').textContent = new Date().getFullYear();
    </script>
    <?php echo $__env->yieldPushContent('scripts'); ?>
</body>
</html>
<?php /**PATH C:\laragon\www\joboctopus\resources\views/frontend/layouts/app.blade.php ENDPATH**/ ?>